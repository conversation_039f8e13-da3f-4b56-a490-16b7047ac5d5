import React, { useState, useEffect } from 'react';
import { getVenueReviews, getVenueRatingStats, getCurrentUser } from '../../lib/pocketbase.ts';
import StarRating from '../common/StarRating.tsx';
import ReviewCard from './ReviewCard.tsx';
import Spinner from '../common/Spinner.tsx';
import type { ReviewWithExpanded, ReviewStats } from '../../types/review.ts';

interface ReviewListProps {
  venueId: string;
  venueOwnerId?: string;
  className?: string;
}

export default function ReviewList({ venueId, venueOwnerId, className = '' }: ReviewListProps) {
  const [reviews, setReviews] = useState<ReviewWithExpanded[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);

  const currentUser = getCurrentUser();
  const isVenueOwner = currentUser?.id === venueOwnerId;

  const loadReviews = async (page: number = 1, append: boolean = false) => {
    try {
      if (page === 1 && !append) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const [reviewsResult, statsResult] = await Promise.all([
        getVenueReviews(venueId, page, 10),
        page === 1 ? getVenueRatingStats(venueId) : Promise.resolve({ success: true, stats })
      ]);

      if (reviewsResult.success && reviewsResult.reviews) {
        if (append) {
          setReviews(prev => [...prev, ...reviewsResult.reviews!.items]);
        } else {
          setReviews(reviewsResult.reviews.items);
        }
        setTotalPages(reviewsResult.reviews.totalPages);
        setCurrentPage(page);
      } else {
        setError(reviewsResult.error || 'Failed to load reviews');
      }

      if (statsResult.success && statsResult.stats) {
        setStats(statsResult.stats);
      }
    } catch (err) {
      setError('An unexpected error occurred');
      console.error('Error loading reviews:', err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleLoadMore = () => {
    if (currentPage < totalPages && !loadingMore) {
      loadReviews(currentPage + 1, true);
    }
  };

  const handleReviewUpdate = () => {
    // Reload reviews when a response is added
    loadReviews(1, false);
  };

  useEffect(() => {
    loadReviews();
  }, [venueId]);

  if (loading) {
    return (
      <div className={`flex justify-center py-8 ${className}`}>
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (!stats || stats.totalReviews === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="bg-slate-50 rounded-xl p-8">
          <h3 className="text-lg font-semibold text-slate-900 mb-2">No reviews yet</h3>
          <p className="text-slate-600">Be the first to share your experience at this venue!</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Reviews Summary */}
      <div className="bg-white rounded-xl border border-slate-200 p-6 shadow-sm">
        <div className="flex items-center gap-4 mb-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-slate-900 mb-1">
              {stats.averageRating.toFixed(1)}
            </div>
            <StarRating
              rating={stats.averageRating}
              readonly
              size="md"
              showValue={false}
            />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              {stats.totalReviews} {stats.totalReviews === 1 ? 'Review' : 'Reviews'}
            </h3>
            <div className="space-y-1">
              {[5, 4, 3, 2, 1].map((rating) => (
                <div key={rating} className="flex items-center gap-2 text-sm">
                  <span className="w-3 text-slate-600">{rating}</span>
                  <div className="flex-1 bg-slate-200 rounded-full h-2">
                    <div
                      className="bg-secondary-400 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${stats.totalReviews > 0 
                          ? (stats.ratingDistribution[rating as keyof typeof stats.ratingDistribution] / stats.totalReviews) * 100 
                          : 0}%`
                      }}
                    />
                  </div>
                  <span className="w-8 text-slate-600 text-right">
                    {stats.ratingDistribution[rating as keyof typeof stats.ratingDistribution]}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.map((review, index) => (
          <div
            key={review.id}
            className="animate-fade-in"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <ReviewCard
              review={review}
              currentUserId={currentUser?.id}
              isVenueOwner={isVenueOwner}
              onResponseAdded={handleReviewUpdate}
            />
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {currentPage < totalPages && (
        <div className="text-center pt-4">
          <button
            type="button"
            onClick={handleLoadMore}
            disabled={loadingMore}
            className="px-6 py-3 bg-white border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 hover:border-slate-400 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loadingMore ? (
              <div className="flex items-center gap-2">
                <Spinner size="sm" />
                Loading more reviews...
              </div>
            ) : (
              `Load more reviews (${totalPages - currentPage} pages remaining)`
            )}
          </button>
        </div>
      )}
    </div>
  );
}
