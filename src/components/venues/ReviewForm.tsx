import React, { useState } from 'react';
import { createReview } from '../../lib/pocketbase.ts';
import StarRating from '../common/StarRating.tsx';
import Modal from '../common/Modal.tsx';
import Button from '../common/Button.tsx';
import type { ReviewFormData } from '../../types/review.ts';

interface ReviewFormProps {
  isOpen: boolean;
  onClose: () => void;
  bookingId: string;
  venueId: string;
  venueName: string;
  renterId: string;
  onReviewSubmitted?: () => void;
}

export default function ReviewForm({
  isOpen,
  onClose,
  bookingId,
  venueId,
  venueName,
  renterId,
  onReviewSubmitted
}: ReviewFormProps) {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (rating === 0) {
      setError('Please select a rating');
      return;
    }

    if (comment.trim().length < 10) {
      setError('Please provide a comment with at least 10 characters');
      return;
    }

    setIsSubmitting(true);
    setError('');

    const reviewData: ReviewFormData = {
      booking: bookingId,
      renter: renterId,
      venue: venueId,
      rating,
      comment: comment.trim()
    };

    try {
      const result = await createReview(reviewData);
      
      if (result.success) {
        // Reset form
        setRating(0);
        setComment('');
        
        // Call callback if provided
        if (onReviewSubmitted) {
          onReviewSubmitted();
        }
        
        // Close modal
        onClose();
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (err) {
      setError('An unexpected error occurred');
      console.error('Review submission error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setRating(0);
      setComment('');
      setError('');
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={`How was your experience at ${venueName}?`}
      size="md"
      closeOnOverlayClick={!isSubmitting}
      closeOnEscape={!isSubmitting}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Rating Section */}
        <div className="text-center">
          <label className="block text-sm font-medium text-slate-700 mb-3">
            Overall Experience
          </label>
          <div className="flex justify-center">
            <StarRating
              rating={rating}
              onRatingChange={setRating}
              size="lg"
              showValue={false}
              className="justify-center"
            />
          </div>
          {rating > 0 && (
            <p className="mt-2 text-sm text-slate-600">
              {rating === 1 && "Poor"}
              {rating === 2 && "Fair"}
              {rating === 3 && "Good"}
              {rating === 4 && "Very Good"}
              {rating === 5 && "Excellent"}
            </p>
          )}
        </div>

        {/* Comment Section */}
        <div>
          <label htmlFor="comment" className="block text-sm font-medium text-slate-700 mb-2">
            Share your experience
          </label>
          <textarea
            id="comment"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Tell others about your experience at this venue..."
            rows={4}
            className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
            disabled={isSubmitting}
            maxLength={1000}
          />
          <div className="flex justify-between mt-1">
            <span className="text-xs text-slate-500">
              Minimum 10 characters
            </span>
            <span className="text-xs text-slate-500">
              {comment.length}/1000
            </span>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={isSubmitting}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting || rating === 0}
            className="flex-1"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Review'}
          </Button>
        </div>
      </form>
    </Modal>
  );
}
