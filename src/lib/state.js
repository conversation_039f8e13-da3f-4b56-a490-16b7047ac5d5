import { atom, map, computed } from 'nanostores';
import { pb } from './pocketbase.ts';

// User authentication state
export const userStore = atom(null);
export const isAuthenticatedStore = atom(false);
export const authLoadingStore = atom(true);

// Computed stores for role checking
export const userRolesStore = computed(userStore, (user) => {
  return user?.roles || [];
});

export const isAdminStore = computed(userRolesStore, (roles) => {
  return roles.includes('admin');
});

export const isOwnerStore = computed(userRolesStore, (roles) => {
  return roles.includes('owner');
});

export const isRenterStore = computed(userRolesStore, (roles) => {
  return roles.includes('renter');
});

// UI state
export const themeStore = atom('light');
export const sidebarOpenStore = atom(false);
export const notificationsStore = atom([]);

// Search state
export const searchQueryStore = atom('');
export const searchResultsStore = atom([]);
export const searchLoadingStore = atom(false);
export const searchFiltersStore = map({
  minPrice: null,
  maxPrice: null,
  minCapacity: null,
  amenities: [],
  location: null,
  sort: 'relevance'
});

// Booking state
export const activeBookingsStore = atom([]);
export const bookingLoadingStore = atom(false);

// Venue state
export const userVenuesStore = atom([]);
export const venueLoadingStore = atom(false);

// Initialize authentication state
export function initializeAuth() {
  authLoadingStore.set(true);
  
  try {
    // Check if user is already authenticated
    if (pb.authStore.isValid && pb.authStore.model) {
      userStore.set(pb.authStore.model);
      isAuthenticatedStore.set(true);

      // Ensure HTTP cookie is set for server-side authentication
      if (typeof document !== 'undefined') {
        const authCookie = JSON.stringify({
          token: pb.authStore.token,
          model: pb.authStore.model
        });
        document.cookie = `pb_auth=${encodeURIComponent(authCookie)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        console.log('Auth init - Auth cookie set for existing session');
      }
    } else {
      userStore.set(null);
      isAuthenticatedStore.set(false);
    }
  } catch (error) {
    console.error('Failed to initialize auth state:', error);
    userStore.set(null);
    isAuthenticatedStore.set(false);
  } finally {
    authLoadingStore.set(false);
  }
}

// Authentication actions
export const authActions = {
  async login(email, password) {
    try {
      authLoadingStore.set(true);
      console.log('Attempting login for:', email);

      const authData = await pb.collection('users').authWithPassword(email, password);
      console.log('Login successful for user:', authData.record.id);

      userStore.set(authData.record);
      isAuthenticatedStore.set(true);

      // Set HTTP cookie for server-side authentication
      if (typeof document !== 'undefined') {
        const authCookie = JSON.stringify({
          token: authData.token,
          model: authData.record
        });
        document.cookie = `pb_auth=${encodeURIComponent(authCookie)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        console.log('Login - Auth cookie set');
      }

      return { success: true, user: authData.record };
    } catch (error) {
      console.error('Login failed:', error);

      // Provide more specific error messages
      let errorMessage = 'Login failed';
      if (error.message) {
        if (error.message.includes('Failed to authenticate')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.';
        } else if (error.message.includes('verification')) {
          errorMessage = 'Please verify your email address before signing in.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else {
          errorMessage = error.message;
        }
      }

      return { success: false, error: errorMessage };
    } finally {
      authLoadingStore.set(false);
    }
  },

  async register(userData) {
    try {
      authLoadingStore.set(true);

      console.log('Registering user with data:', { ...userData, password: '[HIDDEN]' });

      // Create user account with required fields
      const user = await pb.collection('users').create({
        email: userData.email,
        password: userData.password,
        passwordConfirm: userData.passwordConfirm,
        name: userData.name,
        is_active: true,
        roles: ['renter'] // Default role for new users
      });

      console.log('User created successfully:', user.id);

      // Send verification email (optional)
      try {
        await pb.collection('users').requestVerification(userData.email);
        console.log('Verification email sent');
      } catch (verificationError) {
        console.warn('Failed to send verification email:', verificationError);
        // Don't fail registration if verification email fails
      }

      // Try to auto-login after registration
      try {
        console.log('Attempting auto-login after registration');
        const authData = await pb.collection('users').authWithPassword(
          userData.email,
          userData.password
        );

        userStore.set(authData.record);
        isAuthenticatedStore.set(true);

        // Set HTTP cookie for server-side authentication
        if (typeof document !== 'undefined') {
          const authCookie = JSON.stringify({
            token: authData.token,
            model: authData.record
          });
          document.cookie = `pb_auth=${encodeURIComponent(authCookie)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
          console.log('Registration auto-login - Auth cookie set');
        }

        console.log('Auto-login successful');

        return { success: true, user: authData.record, autoLogin: true };
      } catch (loginError) {
        console.warn('Auto-login failed after registration:', loginError);
        // Registration was successful, but auto-login failed
        // User will need to login manually
        return {
          success: true,
          user: user,
          autoLogin: false,
          message: 'Account created successfully! Please sign in with your credentials.'
        };
      }
    } catch (error) {
      console.error('Registration failed:', error);

      // Provide more specific error messages
      let errorMessage = 'Registration failed';
      if (error.message) {
        if (error.message.includes('email')) {
          errorMessage = 'This email address is already registered. Please use a different email or try signing in.';
        } else if (error.message.includes('password')) {
          errorMessage = 'Password does not meet requirements. Please check and try again.';
        } else {
          errorMessage = error.message;
        }
      }

      return { success: false, error: errorMessage };
    } finally {
      authLoadingStore.set(false);
    }
  },

  logout() {
    pb.authStore.clear();
    userStore.set(null);
    isAuthenticatedStore.set(false);

    // Clear HTTP cookie
    if (typeof document !== 'undefined') {
      document.cookie = 'pb_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      console.log('Logout - Auth cookie cleared');
    }

    // Clear other user-specific state
    activeBookingsStore.set([]);
    userVenuesStore.set([]);

    // Redirect to home page
    if (typeof globalThis !== 'undefined' && globalThis.window) {
      globalThis.window.location.href = '/';
    }
  },

  async refreshAuth() {
    try {
      if (pb.authStore.isValid) {
        await pb.collection('users').authRefresh();
        userStore.set(pb.authStore.model);
        isAuthenticatedStore.set(true);
        return { success: true };
      }
    } catch (error) {
      console.error('Auth refresh failed:', error);
      this.logout();
      return { success: false, error: error.message };
    }
  },

  // Role management functions
  async addRole(role) {
    const user = userStore.get();
    if (!user) return { success: false, error: 'No authenticated user' };

    try {
      const currentRoles = user.roles || [];
      if (currentRoles.includes(role)) {
        return { success: true, message: 'Role already exists' };
      }

      const updatedRoles = [...currentRoles, role];
      const updatedUser = await pb.collection('users').update(user.id, {
        roles: updatedRoles
      });

      userStore.set(updatedUser);
      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Failed to add role:', error);
      return { success: false, error: error.message };
    }
  },

  async removeRole(role) {
    const user = userStore.get();
    if (!user) return { success: false, error: 'No authenticated user' };

    try {
      const currentRoles = user.roles || [];
      const updatedRoles = currentRoles.filter(r => r !== role);

      // Ensure user always has at least 'renter' role
      if (updatedRoles.length === 0) {
        updatedRoles.push('renter');
      }

      const updatedUser = await pb.collection('users').update(user.id, {
        roles: updatedRoles
      });

      userStore.set(updatedUser);
      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Failed to remove role:', error);
      return { success: false, error: error.message };
    }
  },

  // Check if current user has specific role
  hasRole(role) {
    const user = userStore.get();
    return user?.roles?.includes(role) || false;
  },

  // Check if current user is admin
  isAdmin() {
    return this.hasRole('admin');
  },

  // Check if current user is owner
  isOwner() {
    return this.hasRole('owner');
  },

  // Update user profile
  async updateProfile(profileData) {
    const user = userStore.get();
    if (!user) return { success: false, error: 'No authenticated user' };

    try {
      const updatedUser = await pb.collection('users').update(user.id, profileData);
      userStore.set(updatedUser);
      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Failed to update profile:', error);
      return { success: false, error: error.message };
    }
  }
};

// Search actions
export const searchActions = {
  setQuery(query) {
    searchQueryStore.set(query);
  },

  setResults(results) {
    searchResultsStore.set(results);
  },

  setLoading(loading) {
    searchLoadingStore.set(loading);
  },

  updateFilters(filters) {
    const currentFilters = searchFiltersStore.get();
    searchFiltersStore.set({ ...currentFilters, ...filters });
  },

  clearFilters() {
    searchFiltersStore.set({
      minPrice: null,
      maxPrice: null,
      minCapacity: null,
      amenities: [],
      location: null,
      sort: 'relevance'
    });
  }
};

// Notification actions
export const notificationActions = {
  add(notification) {
    const notifications = notificationsStore.get();
    const newNotification = {
      id: Date.now().toString(),
      timestamp: new Date(),
      ...notification
    };
    notificationsStore.set([...notifications, newNotification]);
    
    // Auto-remove after 5 seconds for non-persistent notifications
    if (!notification.persistent) {
      setTimeout(() => {
        this.remove(newNotification.id);
      }, 5000);
    }
  },

  remove(id) {
    const notifications = notificationsStore.get();
    notificationsStore.set(notifications.filter(n => n.id !== id));
  },

  clear() {
    notificationsStore.set([]);
  }
};

// Theme actions
export const themeActions = {
  setTheme(theme) {
    themeStore.set(theme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', theme);
      document.documentElement.setAttribute('data-theme', theme);
    }
  },

  toggleTheme() {
    const currentTheme = themeStore.get();
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  },

  initializeTheme() {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') || 'light';
      this.setTheme(savedTheme);
    }
  }
};

// UI actions
export const uiActions = {
  toggleSidebar() {
    sidebarOpenStore.set(!sidebarOpenStore.get());
  },

  setSidebarOpen(open) {
    sidebarOpenStore.set(open);
  }
};

// Data loading actions
export const dataActions = {
  async loadUserBookings() {
    const user = userStore.get();
    if (!user) return;

    try {
      bookingLoadingStore.set(true);
      const bookings = await pb.collection('bookings').getList(1, 50, {
        filter: `renter.id = "${user.id}" || venue.owner.id = "${user.id}"`,
        expand: 'venue,renter,venue.owner',
        sort: '-created'
      });
      
      activeBookingsStore.set(bookings.items);
      return { success: true, bookings: bookings.items };
    } catch (error) {
      console.error('Failed to load bookings:', error);
      return { success: false, error: error.message };
    } finally {
      bookingLoadingStore.set(false);
    }
  },

  async loadUserVenues() {
    const user = userStore.get();
    if (!user) return;

    try {
      venueLoadingStore.set(true);
      const venues = await pb.collection('venues').getList(1, 50, {
        filter: `owner.id = "${user.id}"`,
        sort: '-created'
      });
      
      userVenuesStore.set(venues.items);
      return { success: true, venues: venues.items };
    } catch (error) {
      console.error('Failed to load venues:', error);
      return { success: false, error: error.message };
    } finally {
      venueLoadingStore.set(false);
    }
  }
};

// Initialize stores on app load
if (typeof window !== 'undefined') {
  // Initialize auth state
  initializeAuth();
  
  // Initialize theme
  themeActions.initializeTheme();
  
  // Listen for auth changes
  pb.authStore.onChange(() => {
    if (pb.authStore.isValid && pb.authStore.model) {
      userStore.set(pb.authStore.model);
      isAuthenticatedStore.set(true);

      // Sync HTTP cookie when auth changes
      if (typeof document !== 'undefined') {
        const authCookie = JSON.stringify({
          token: pb.authStore.token,
          model: pb.authStore.model
        });
        document.cookie = `pb_auth=${encodeURIComponent(authCookie)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
        console.log('Auth change - Auth cookie synced');
      }
    } else {
      userStore.set(null);
      isAuthenticatedStore.set(false);

      // Clear HTTP cookie when auth is cleared
      if (typeof document !== 'undefined') {
        document.cookie = 'pb_auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        console.log('Auth change - Auth cookie cleared');
      }
    }
  });
}

// Export all stores and actions
export default {
  // Stores
  userStore,
  isAuthenticatedStore,
  authLoadingStore,
  userRolesStore,
  isAdminStore,
  isOwnerStore,
  isRenterStore,
  themeStore,
  sidebarOpenStore,
  notificationsStore,
  searchQueryStore,
  searchResultsStore,
  searchLoadingStore,
  searchFiltersStore,
  activeBookingsStore,
  bookingLoadingStore,
  userVenuesStore,
  venueLoadingStore,

  // Actions
  authActions,
  searchActions,
  notificationActions,
  themeActions,
  uiActions,
  dataActions
};
